{"name": "<PERSON><PERSON>-manager-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "openapi": "npx tsx openapi.config.ts"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@monaco-editor/react": "^4.6.0", "antd": "^5.12.8", "axios": "^1.6.2", "dayjs": "^1.11.10", "monaco-editor": "^0.45.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "recharts": "^2.8.0", "zustand": "^4.4.7"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@umijs/openapi": "^1.13.15", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^5.0.0", "vitest": "^1.0.0"}}