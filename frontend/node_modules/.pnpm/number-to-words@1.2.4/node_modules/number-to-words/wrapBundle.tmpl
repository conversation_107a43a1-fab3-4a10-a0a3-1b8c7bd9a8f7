/*!
 * Number-To-Words util
 * @version v<%= data.version %>
 * @link <%= data.homepage %>
 * <AUTHOR> data.author %>
 * @contributors <%= data.contributors %>
 * @license <%= data.license %>
 */
(function () {
    'use strict';

    var root = typeof self == 'object' && self.self === self && self ||
        typeof global == 'object' && global.global === global && global ||
        this;

    <%= data.contents %>

    var numberToWords = {
        toOrdinal: toOrdinal,
        toWords: toWords,
        toWordsOrdinal: toWordsOrdinal
    };

    if (typeof exports != 'undefined') {
        if (typeof module != 'undefined' && module.exports) {
            exports = module.exports = numberToWords;
        }
        exports.numberToWords = numberToWords;
    } else {
        root.numberToWords = numberToWords;
    }

}());
