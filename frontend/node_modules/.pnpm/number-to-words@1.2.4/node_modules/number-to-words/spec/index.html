<!doctype html>
<html>
    <head>
        <title>Jasmine <PERSON> Runner</title>
        <link rel="stylesheet" href="../node_modules/jasmine-core/lib/jasmine-core/jasmine.css">
        <script src="../node_modules/jasmine-core/lib/jasmine-core/jasmine.js"></script>
        <script src="../node_modules/jasmine-core/lib/jasmine-core/jasmine-html.js"></script>
        <script src="../node_modules/jasmine-core/lib/jasmine-core/boot.js"></script>
        <script src="../numberToWords.js"></script>
        <script src="indexSpec.js"></script>
        <script src="toOrdinalSpec.js"></script>
        <script src="toWordsOrdinalSpec.js"></script>
        <script src="toWordsSpec.js"></script>
    </head>
    <body>
    </body>
</html>
