{"name": "number-to-words", "description": "Contains some util methods for converting numbers into words, ordinal words and ordinal numbers.", "version": "1.2.4", "main": "./numberToWords.min.js", "authors": ["<PERSON> (https://github.com/marlun78)"], "contributors": ["<PERSON><PERSON><PERSON> (https://github.com/pilyugin)", "Jeremiah Hall (https://github.com/jeremiahrhall)", "<PERSON><PERSON> (https://github.com/adrianomelo)", "dmrzn (https://github.com/dmrzn)"], "homepage": "https://github.com/marlun78/number-to-words", "moduleType": ["globals", "node"], "repository": {"type": "git", "url": "https://github.com/marlun78/number-to-words"}, "keywords": ["converter", "number", "ordinal", "string", "tool", "word"], "license": "MIT", "ignore": ["bower_components", "node_modules", "spec", ".editorconfig", ".es<PERSON>", ".npmrc", ".prettier<PERSON>", "gulpfile.js", "wrapBundle.tmpl", "wrapEach.tmpl"]}