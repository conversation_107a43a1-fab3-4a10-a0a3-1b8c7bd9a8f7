{"name": "should", "description": "test framework agnostic BDD-style assertions", "version": "13.2.3", "author": "<PERSON><PERSON> <<EMAIL>>, <PERSON> <bardad<PERSON><PERSON><PERSON>@gmail.com>", "typings": "./should.d.ts", "repository": {"type": "git", "url": "https://github.com/shouldjs/should.js.git"}, "homepage": "https://github.com/shouldjs/should.js", "scripts": {"build:cjs": "rollup --output.format=cjs --output.file=cjs/should.js lib/index.js", "build:es6": "rollup --output.format=es --output.file=es6/should.js lib/index.js", "build": "npm run build:cjs && npm run build:es6", "prepare": "npm run build && npm run build:umd && npm run build:cjs:as-function", "pretest": "npm run build", "build:cjs:as-function": "rollup --input ./lib/should.js --output.format=cjs --output.file ./as-function.js", "test": "mocha -<PERSON> mocha-better-spec-reporter --require ./cjs/should --color --check-leaks ./test/*.test.js ./test/**/*.test.js", "zuul": "zuul -- ./test/**/*.test.js ./test/*.test.js", "build:umd": "rollup -c rollup.config.js --input ./lib/umd.js --output.format=iife  --output.file ./should.js"}, "devDependencies": {"bluebird": "^3.5.1", "eslint": "^3.19.0", "eslint-config-shouldjs": "^1.0.0", "mocha": "^4.0.1", "mocha-better-spec-reporter": "latest", "prettier": "^1.7.4", "rollup": "^0.53.0", "rollup-plugin-node-resolve": "^3.0.0", "tslint": "^5.8.0", "typescript": "^2.5.3", "zuul": "latest"}, "keywords": ["test", "bdd", "assert", "should"], "main": "./cjs/should.js", "module": "./es6/should.js", "license": "MIT", "dependencies": {"should-equal": "^2.0.0", "should-format": "^3.0.3", "should-type": "^1.4.0", "should-type-adaptors": "^1.0.1", "should-util": "^1.0.0"}, "files": ["cjs/*", "es6/*", "as-function.js", "should.js", "LICENSE", "*.md", "should.d.ts"]}