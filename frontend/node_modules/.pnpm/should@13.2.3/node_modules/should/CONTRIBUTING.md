Before contribute something:

Follow simple rules:

- Not violate [DRY](http://programmer.97things.oreilly.com/wiki/index.php/Don%27t_Repeat_Yourself).
- [Boy Scout Rule](http://programmer.97things.oreilly.com/wiki/index.php/The_Boy_Scout_Rule) needs to have been applied.

1. Your code should look like all the other code - this project should look like it was written by one person, always.
2. If you want to propose something - just create an issue and describe your question with as much description as you can.
3. Please never send issues or pull requests about code style, jshint violations etc - I do not accept it (and you will spend your time for free).
4. If you think you have some general improvement, consider creating a pull request with it.
5. If you are not sure whether your improvement is general enough, just create your own plugin for should.js. (see should.use and Assertion.add usage).
6. If you add new code, it should be covered by tests. No tests - no code.
7. If you find a bug (or at least you think it is a bug), create an issue with the library version and test case that I can run and see what are you talking about, or at least full steps by which I can reproduce it.
