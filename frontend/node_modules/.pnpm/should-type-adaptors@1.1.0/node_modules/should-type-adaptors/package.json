{"name": "should-type-adaptors", "version": "1.1.0", "description": "Small utility functions to use the same traversing etc code on different types", "main": "cjs/should-type-adaptors.js", "jsnext:main": "es6/should-type-adaptors.js", "dependencies": {"should-type": "^1.3.0", "should-util": "^1.0.0"}, "devDependencies": {"eslint": "^3.2.2", "eslint-config-shouldjs": "^1.0.2", "rollup": "^0.34.7"}, "scripts": {"cjs": "rollup --format=cjs --output=cjs/should-type-adaptors.js index.js", "es6": "rollup --format=es --output=es6/should-type-adaptors.js index.js", "build": "npm run --silent cjs && npm run --silent es6", "prepublish": "npm run --silent build", "pretest": "npm run --silent build"}, "repository": {"type": "git", "url": "git+https://github.com/shouldjs/type-adaptors.git"}, "keywords": ["<PERSON>js", "type"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/shouldjs/type-adaptors/issues"}, "homepage": "https://github.com/shouldjs/type-adaptors#readme", "files": ["cjs/*", "es6/*", "README.md", "LICENSE"]}