{"name": "oas-schema-walker", "version": "1.1.5", "description": "Library to walk OAS 3 schema objects and call a callback", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "funding": "https://github.com/Mermade/oas-kit?sponsor=1", "keywords": ["openapi", "swagger", "oas", "schema", "json-schema"], "author": "<PERSON>", "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "https://github.com/Mermade/oas-kit.git"}, "bugs": {"url": "https://github.com/mermade/oas-kit/issues"}, "gitHead": "dc53888b6184e0896ac878a39fec92f835fd44a3"}