# OAS-Schema-Walker
## Functions

<dl>
<dt><a href="#getDefaultState">getDefaultState()</a> ⇒</dt>
<dd><p>obtains the default starting state for the <code>state</code> object used
by walkSchema</p>
</dd>
<dt><a href="#walkSchema">walkSchema(parent, state, callback,)</a> ⇒</dt>
<dd><p>begins the walk of a schema object, or the <code>state</code> object used
by walkSchema</p>
</dd>
</dl>

<a name="getDefaultState"></a>

## getDefaultState() ⇒
obtains the default starting state for the `state` object used
by walkSchema

**Kind**: global function
**Returns**: the state object suitable for use in walkSchema
<a name="walkSchema"></a>

## walkSchema(parent, state, callback,) ⇒
begins the walk of a schema object, or the `state` object used
by walkSchema

**Kind**: global function
**Returns**: the schema object

| Param | Description |
| --- | --- |
| parent | the parent schema, if any. Use empty object if none |
| state | the initial starting state of the walker, usually obtained from `getDefaultState` |
| callback, | a function taking a schema, parent and state to be called on this and all subschemas |

