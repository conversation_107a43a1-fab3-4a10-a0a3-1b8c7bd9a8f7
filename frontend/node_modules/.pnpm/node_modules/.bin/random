#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/code/vanna-manger/frontend/node_modules/.pnpm/mockjs@1.1.0/node_modules/mockjs/bin/node_modules:/Users/<USER>/code/vanna-manger/frontend/node_modules/.pnpm/mockjs@1.1.0/node_modules/mockjs/node_modules:/Users/<USER>/code/vanna-manger/frontend/node_modules/.pnpm/mockjs@1.1.0/node_modules:/Users/<USER>/code/vanna-manger/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/code/vanna-manger/frontend/node_modules/.pnpm/mockjs@1.1.0/node_modules/mockjs/bin/node_modules:/Users/<USER>/code/vanna-manger/frontend/node_modules/.pnpm/mockjs@1.1.0/node_modules/mockjs/node_modules:/Users/<USER>/code/vanna-manger/frontend/node_modules/.pnpm/mockjs@1.1.0/node_modules:/Users/<USER>/code/vanna-manger/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../mockjs/bin/random" "$@"
else
  exec node  "$basedir/../mockjs/bin/random" "$@"
fi
