#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/code/vanna-manger/frontend/node_modules/.pnpm/nunjucks@3.2.4/node_modules/nunjucks/bin/node_modules:/Users/<USER>/code/vanna-manger/frontend/node_modules/.pnpm/nunjucks@3.2.4/node_modules/nunjucks/node_modules:/Users/<USER>/code/vanna-manger/frontend/node_modules/.pnpm/nunjucks@3.2.4/node_modules:/Users/<USER>/code/vanna-manger/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/code/vanna-manger/frontend/node_modules/.pnpm/nunjucks@3.2.4/node_modules/nunjucks/bin/node_modules:/Users/<USER>/code/vanna-manger/frontend/node_modules/.pnpm/nunjucks@3.2.4/node_modules/nunjucks/node_modules:/Users/<USER>/code/vanna-manger/frontend/node_modules/.pnpm/nunjucks@3.2.4/node_modules:/Users/<USER>/code/vanna-manger/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../nunjucks/bin/precompile" "$@"
else
  exec node  "$basedir/../nunjucks/bin/precompile" "$@"
fi
