{"name": "es6-promise", "namespace": "es6-promise", "version": "3.3.1", "description": "A lightweight library that provides tools for organizing asynchronous code", "main": "dist/es6-promise.js", "typings": "es6-promise.d.ts", "directories": {"lib": "lib"}, "files": ["dist", "lib", "es6-promise.d.ts", "!dist/test"], "devDependencies": {"broccoli-babel-transpiler": "^5.6.1", "broccoli-concat": "^3.0.2", "broccoli-merge-trees": "^1.1.1", "broccoli-rollup": "^1.0.2", "broccoli-stew": "^1.2.0", "broccoli-uglify-js": "^0.2.0", "broccoli-watchify": "v1.0.0", "ember-cli": "^2.7.0", "ember-cli-dependency-checker": "^1.3.0", "ember-publisher": "0.0.7", "git-repo-version": "0.4.0", "json3": "^3.3.2", "mocha": "^1.20.1", "promises-aplus-tests-phantom": "^2.1.0-revise", "release-it": "0.0.10"}, "scripts": {"build": "ember build --environment production", "build:production": "ember build --env production", "start": "ember s", "test": "ember test", "test:server": "ember test --server", "test:node": "ember build && mocha ./dist/test/browserify", "prepublish": "ember build --environment production", "lint": "jshint lib", "dry-run-release": "ember build --environment production && release-it --dry-run --non-interactive"}, "repository": {"type": "git", "url": "git://github.com/stefanpenner/es6-promise.git"}, "bugs": {"url": "https://github.com/stefanpenner/es6-promise/issues"}, "browser": {"vertx": false}, "keywords": ["promises", "futures"], "author": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and contributors (Conversion to ES6 API by <PERSON>)", "license": "MIT", "spm": {"main": "dist/es6-promise.js"}}