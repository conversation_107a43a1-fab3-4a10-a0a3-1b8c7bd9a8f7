#!/usr/bin/env node
var {program} = require('commander');
var precompile = require('../src/precompile').precompile;
var Environment = require('../src/environment').Environment;
var lib = require('../src/lib');

var cmdpath = null;

program
  .storeOptionsAsProperties(false)
  .passCommandToAction(false);

program
  .name('precompile')
  .usage('[-f|--force] [-a|--filters <filters>] [-n|--name <name>] [-i|--include <regex>] [-x|--exclude <regex>] [-w|--wrapper <wrapper>] <path>')
  .arguments('<path>')
  .helpOption('-?, -h, --help', 'Display this help message')
  .option('-f, --force', 'Force compilation to continue on error')
  .option('-a, --filters <filters>', 'Give the compiler a comma-delimited list of asynchronous filters, required for correctly generating code')
  .option('-n, --name <name>', 'Specify the template name when compiling a single file')
  .option('-i, --include <regex>', 'Include a file or folder which match the regex but would otherwise be excluded. You can use this flag multiple times', concat, ['\\.html$', '\\.jinja$'])
  .option('-x, --exclude <regex>', 'Exclude a file or folder which match the regex but would otherwise be included. You can use this flag multiple times', concat, [])
  .option('-w, --wrapper <wrapper>', 'Load a external plugin to change the output format of the precompiled templates (for example, "-w custom" will load a module named "nunjucks-custom")')
  .action(function (path) {
    cmdpath = path;
  })
  .parse(process.argv);

function concat(value, previous) {
  return previous.concat(value);
}

if (cmdpath == null) {
  program.outputHelp();
  console.error('\nerror: no path given');
  process.exit(1);
}

var env = new Environment([]);

const opts = program.opts();

lib.each([].concat(opts.filters).join(',').split(','), function (name) {
  env.addFilter(name.trim(), function () {}, true);
});

if (opts.wrapper) {
  opts.wrapper = require('nunjucks-' + opts.wrapper).wrapper;
}

console.log(precompile(cmdpath, {
  env : env,
  force : opts.force,
  name : opts.name,
  wrapper: opts.wrapper,
  include : [].concat(opts.include),
  exclude : [].concat(opts.exclude)
}));
