{"name": "should-format", "version": "3.0.3", "description": "Formatting of objects for should.js", "main": "cjs/should-format.js", "jsnext:main": "es6/should-format.js", "repository": {"type": "git", "url": "git://github.com/shouldjs/format.git"}, "scripts": {"test": "mocha --harmony_simd -R mocha-better-spec-reporter tests/test.js", "browserify": "rm tests/browser/tests.js && browserify tests/test.js >> tests/browser/tests.js", "cjs": "rollup --format=cjs --output=cjs/should-format.js index.js", "es6": "rollup --format=es --output=es6/should-format.js index.js", "build": "npm run cjs && npm run es6", "prepublish": "npm run build", "pretest": "npm run build"}, "keywords": ["should", "format"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"browserify": "latest", "eslint": "^3.2.2", "eslint-config-shouldjs": "^1.0.2", "mocha": "latest", "mocha-better-spec-reporter": "latest", "rollup": "0.34.7"}, "files": ["cjs/*", "es6/*", "README.md", "LICENSE"], "dependencies": {"should-type": "^1.3.0", "should-type-adaptors": "^1.0.1"}}