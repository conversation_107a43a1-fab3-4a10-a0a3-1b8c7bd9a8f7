#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/code/vanna-manger/frontend/node_modules/.pnpm/oas-resolver@2.5.6/node_modules/oas-resolver/node_modules:/Users/<USER>/code/vanna-manger/frontend/node_modules/.pnpm/oas-resolver@2.5.6/node_modules:/Users/<USER>/code/vanna-manger/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/code/vanna-manger/frontend/node_modules/.pnpm/oas-resolver@2.5.6/node_modules/oas-resolver/node_modules:/Users/<USER>/code/vanna-manger/frontend/node_modules/.pnpm/oas-resolver@2.5.6/node_modules:/Users/<USER>/code/vanna-manger/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../oas-resolver@2.5.6/node_modules/oas-resolver/resolve.js" "$@"
else
  exec node  "$basedir/../../../../../oas-resolver@2.5.6/node_modules/oas-resolver/resolve.js" "$@"
fi
