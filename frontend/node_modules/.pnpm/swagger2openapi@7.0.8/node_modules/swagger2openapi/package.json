{"name": "swagger2openapi", "version": "7.0.8", "description": "Convert Swagger 2.0 definitions to OpenApi 3.0 and validate", "main": "index.js", "bin": {"swagger2openapi": "./swagger2openapi.js", "oas-validate": "./oas-validate.js", "boast": "./boast.js"}, "funding": "https://github.com/Mermade/oas-kit?sponsor=1", "scripts": {"test": "mocha"}, "browserify": {"transform": [["babe<PERSON>", {"presets": ["es2015"]}]]}, "repository": {"url": "https://github.com/Mermade/oas-kit.git", "type": "git"}, "bugs": {"url": "https://github.com/mermade/oas-kit/issues"}, "author": "<PERSON> <<EMAIL>>", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"call-me-maybe": "^1.0.1", "node-fetch": "^2.6.1", "node-fetch-h2": "^2.3.0", "node-readfiles": "^0.2.0", "oas-kit-common": "^1.0.8", "oas-resolver": "^2.5.6", "oas-schema-walker": "^1.1.5", "oas-validator": "^5.0.8", "reftools": "^1.1.9", "yaml": "^1.10.0", "yargs": "^17.0.1"}, "keywords": ["swagger", "openapi", "openapi2", "openapi3", "converter", "conversion", "validator", "validation", "resolver", "lint", "linter"], "gitHead": "b1bba3fc5007e96a991bf2a015cf0534ac36b88b"}