"use strict";

module.exports = {
	"#": require("./#"),
	"EPSILON": require("./epsilon"),
	"isFinite": require("./is-finite"),
	"isInteger": require("./is-integer"),
	"isNaN": require("./is-nan"),
	"isNatural": require("./is-natural"),
	"isNumber": require("./is-number"),
	"isSafeInteger": require("./is-safe-integer"),
	"MAX_SAFE_INTEGER": require("./max-safe-integer"),
	"MIN_SAFE_INTEGER": require("./min-safe-integer"),
	"toInteger": require("./to-integer"),
	"toPosInteger": require("./to-pos-integer"),
	"toUint32": require("./to-uint32")
};
