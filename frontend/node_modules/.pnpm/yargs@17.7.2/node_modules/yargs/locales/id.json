{"Commands:": "<PERSON><PERSON><PERSON>:", "Options:": "Pilihan:", "Examples:": "Contoh:", "boolean": "boolean", "count": "jumlah", "number": "nomor", "string": "string", "array": "la<PERSON>", "required": "diperlukan", "default": "bawaan", "default:": "bawaan:", "aliases:": "istilah lain:", "choices:": "pilihan:", "generated-value": "nilai-yang-<PERSON><PERSON><PERSON><PERSON>", "Not enough non-option arguments: got %s, need at least %s": {"one": "Argumen wajib kurang: hanya %s, minimal %s", "other": "Argumen wajib kurang: hanya %s, minimal %s"}, "Too many non-option arguments: got %s, maximum of %s": {"one": "Terlalu banyak argumen wajib: ada %s, maksimal %s", "other": "Terlalu banyak argumen wajib: ada %s, maksimal %s"}, "Missing argument value: %s": {"one": "Kurang argumen: %s", "other": "Kurang argumen: %s"}, "Missing required argument: %s": {"one": "Kurang argumen wajib: %s", "other": "Kurang argumen wajib: %s"}, "Unknown argument: %s": {"one": "Argumen tak diketahui: %s", "other": "Argumen tak diketahui: %s"}, "Invalid values:": "Nilai-<PERSON>lai tidak valid:", "Argument: %s, Given: %s, Choices: %s": "Argumen: %s, Diberikan: %s, Pilihan: %s", "Argument check failed: %s": "Pemeriksaan argument gagal: %s", "Implications failed:": "Implikasi gagal:", "Not enough arguments following: %s": "Kurang argumen untuk: %s", "Invalid JSON config file: %s": "Berkas konfigurasi JSON tidak valid: %s", "Path to JSON config file": "<PERSON><PERSON><PERSON> be<PERSON> konfigurasi JSON", "Show help": "<PERSON><PERSON>", "Show version number": "<PERSON>hat nomor versi", "Did you mean %s?": "<PERSON><PERSON><PERSON>: %s?", "Arguments %s and %s are mutually exclusive": "Argumen %s dan %s saling eksklusif", "Positionals:": "Posisional-posisional:", "command": "<PERSON><PERSON><PERSON>"}