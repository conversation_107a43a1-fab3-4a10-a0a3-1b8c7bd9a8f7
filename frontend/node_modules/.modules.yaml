hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@ant-design/colors@7.2.1':
    '@ant-design/colors': private
  '@ant-design/cssinjs-utils@1.1.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@ant-design/cssinjs-utils': private
  '@ant-design/cssinjs@1.23.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@ant-design/cssinjs': private
  '@ant-design/fast-color@2.0.6':
    '@ant-design/fast-color': private
  '@ant-design/icons-svg@4.4.2':
    '@ant-design/icons-svg': private
  '@ant-design/react-slick@1.1.2(react@18.3.1)':
    '@ant-design/react-slick': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.0':
    '@babel/core': private
  '@babel/generator@7.28.0':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/runtime@7.27.6':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.0':
    '@babel/traverse': private
  '@babel/types@7.28.1':
    '@babel/types': private
  '@emotion/hash@0.8.0':
    '@emotion/hash': private
  '@emotion/unitless@0.7.5':
    '@emotion/unitless': private
  '@esbuild/aix-ppc64@0.21.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.21.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.21.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.21.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.21.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.21.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.21.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.21.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.21.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.21.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.21.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.21.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.21.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.21.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.21.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.21.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.21.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-x64@0.21.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-x64@0.21.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.21.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.21.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.21.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.21.5':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@8.57.1)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': private
  '@eslint/js@8.57.1':
    '@eslint/js': private
  '@exodus/schemasafe@1.3.0':
    '@exodus/schemasafe': private
  '@humanwhocodes/config-array@0.13.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@jest/schemas@29.6.3':
    '@jest/schemas': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@monaco-editor/loader@1.5.0':
    '@monaco-editor/loader': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@rc-component/async-validator@5.0.4':
    '@rc-component/async-validator': private
  '@rc-component/color-picker@2.0.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/color-picker': private
  '@rc-component/context@1.4.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/context': private
  '@rc-component/mini-decimal@1.1.0':
    '@rc-component/mini-decimal': private
  '@rc-component/mutate-observer@1.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/mutate-observer': private
  '@rc-component/portal@1.1.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/portal': private
  '@rc-component/qrcode@1.0.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/qrcode': private
  '@rc-component/tour@1.15.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/tour': private
  '@rc-component/trigger@2.2.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/trigger': private
  '@remix-run/router@1.23.0':
    '@remix-run/router': private
  '@rolldown/pluginutils@1.0.0-beta.19':
    '@rolldown/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.45.0':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.45.0':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.45.0':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.45.0':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.45.0':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.45.0':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.45.0':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.45.0':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.45.0':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.45.0':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.45.0':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.45.0':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.45.0':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.45.0':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.45.0':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.45.0':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.45.0':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.45.0':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.45.0':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.45.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@sinclair/typebox@0.27.8':
    '@sinclair/typebox': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.7':
    '@types/babel__traverse': private
  '@types/d3-array@3.2.1':
    '@types/d3-array': private
  '@types/d3-color@3.1.3':
    '@types/d3-color': private
  '@types/d3-ease@3.0.2':
    '@types/d3-ease': private
  '@types/d3-interpolate@3.0.4':
    '@types/d3-interpolate': private
  '@types/d3-path@3.1.1':
    '@types/d3-path': private
  '@types/d3-scale@4.0.9':
    '@types/d3-scale': private
  '@types/d3-shape@3.1.7':
    '@types/d3-shape': private
  '@types/d3-time@3.0.4':
    '@types/d3-time': private
  '@types/d3-timer@3.0.2':
    '@types/d3-timer': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/prop-types@15.7.15':
    '@types/prop-types': private
  '@types/semver@7.7.0':
    '@types/semver': private
  '@typescript-eslint/scope-manager@6.21.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@6.21.0(eslint@8.57.1)(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@6.21.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@6.21.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@6.21.0(eslint@8.57.1)(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@6.21.0':
    '@typescript-eslint/visitor-keys': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@vitest/expect@1.6.1':
    '@vitest/expect': private
  '@vitest/runner@1.6.1':
    '@vitest/runner': private
  '@vitest/snapshot@1.6.1':
    '@vitest/snapshot': private
  '@vitest/spy@1.6.1':
    '@vitest/spy': private
  '@vitest/utils@1.6.1':
    '@vitest/utils': private
  a-sync-waterfall@1.0.1:
    a-sync-waterfall: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  argparse@2.0.1:
    argparse: private
  array-union@2.1.0:
    array-union: private
  asap@2.0.6:
    asap: private
  assertion-error@1.1.0:
    assertion-error: private
  asynckit@0.4.0:
    asynckit: private
  balanced-match@1.0.2:
    balanced-match: private
  big-integer@1.6.52:
    big-integer: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  broadcast-channel@3.7.0:
    broadcast-channel: private
  browserslist@4.25.1:
    browserslist: private
  cac@6.7.14:
    cac: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-me-maybe@1.0.2:
    call-me-maybe: private
  callsites@3.1.0:
    callsites: private
  caniuse-lite@1.0.30001727:
    caniuse-lite: private
  chai@4.5.0:
    chai: private
  chalk@4.1.2:
    chalk: private
  check-error@1.0.3:
    check-error: private
  classnames@2.5.1:
    classnames: private
  cliui@8.0.1:
    cliui: private
  clsx@2.1.1:
    clsx: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@14.0.0:
    commander: private
  compute-scroll-into-view@3.1.1:
    compute-scroll-into-view: private
  concat-map@0.0.1:
    concat-map: private
  confbox@0.1.8:
    confbox: private
  convert-source-map@2.0.0:
    convert-source-map: private
  copy-to-clipboard@3.3.3:
    copy-to-clipboard: private
  cosmiconfig@9.0.0(typescript@5.8.3):
    cosmiconfig: private
  cross-spawn@7.0.6:
    cross-spawn: private
  csstype@3.1.3:
    csstype: private
  d3-array@3.2.4:
    d3-array: private
  d3-color@3.1.0:
    d3-color: private
  d3-ease@3.0.1:
    d3-ease: private
  d3-format@3.1.0:
    d3-format: private
  d3-interpolate@3.0.1:
    d3-interpolate: private
  d3-path@3.1.0:
    d3-path: private
  d3-scale@4.0.2:
    d3-scale: private
  d3-shape@3.2.0:
    d3-shape: private
  d3-time-format@4.1.0:
    d3-time-format: private
  d3-time@3.1.0:
    d3-time: private
  d3-timer@3.0.1:
    d3-timer: private
  d@1.0.2:
    d: private
  debug@4.4.1:
    debug: private
  decimal.js-light@2.5.1:
    decimal.js-light: private
  deep-eql@4.1.4:
    deep-eql: private
  deep-is@0.1.4:
    deep-is: private
  delayed-stream@1.0.0:
    delayed-stream: private
  detect-node@2.1.0:
    detect-node: private
  diff-sequences@29.6.3:
    diff-sequences: private
  dir-glob@3.0.1:
    dir-glob: private
  doctrine@3.0.0:
    doctrine: private
  dom-helpers@5.2.1:
    dom-helpers: private
  dunder-proto@1.0.1:
    dunder-proto: private
  electron-to-chromium@1.5.183:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  env-paths@2.2.1:
    env-paths: private
  error-ex@1.3.2:
    error-ex: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es5-ext@0.10.64:
    es5-ext: private
  es6-iterator@2.0.3:
    es6-iterator: private
  es6-promise@3.3.1:
    es6-promise: private
  es6-symbol@3.1.4:
    es6-symbol: private
  es6-weak-map@2.0.3:
    es6-weak-map: private
  esbuild@0.21.5:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@7.2.2:
    eslint-scope: private
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: private
  esniff@2.0.1:
    esniff: private
  espree@9.6.1:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@3.0.3:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  event-emitter@0.3.5:
    event-emitter: private
  eventemitter3@4.0.7:
    eventemitter3: private
  execa@8.0.1:
    execa: private
  ext@1.7.0:
    ext: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-equals@5.2.2:
    fast-equals: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-safe-stringify@2.1.1:
    fast-safe-stringify: private
  fastq@1.19.1:
    fastq: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  follow-redirects@1.15.9:
    follow-redirects: private
  form-data@4.0.3:
    form-data: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-func-name@2.0.2:
    get-func-name: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@8.0.1:
    get-stream: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@7.2.3:
    glob: private
  globals@13.24.0:
    globals: private
  globby@11.1.0:
    globby: private
  gopd@1.2.0:
    gopd: private
  graphemer@1.4.0:
    graphemer: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  http2-client@1.3.5:
    http2-client: private
  human-signals@5.0.0:
    human-signals: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  internmap@2.0.3:
    internmap: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-promise@2.2.2:
    is-promise: private
  is-stream@3.0.0:
    is-stream: private
  isexe@2.0.0:
    isexe: private
  js-sha3@0.8.0:
    js-sha3: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json2mq@0.2.0:
    json2mq: private
  json5@2.2.3:
    json5: private
  keyv@4.5.4:
    keyv: private
  levn@0.4.1:
    levn: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  local-pkg@0.5.1:
    local-pkg: private
  locate-path@6.0.0:
    locate-path: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash@4.17.21:
    lodash: private
  loose-envify@1.4.0:
    loose-envify: private
  loupe@2.3.7:
    loupe: private
  lru-cache@5.1.1:
    lru-cache: private
  lru-queue@0.1.0:
    lru-queue: private
  magic-string@0.30.17:
    magic-string: private
  match-sorter@6.3.4:
    match-sorter: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  memoizee@0.4.17:
    memoizee: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  microseconds@0.2.0:
    microseconds: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mimic-fn@4.0.0:
    mimic-fn: private
  minimatch@3.1.2:
    minimatch: private
  mlly@1.7.4:
    mlly: private
  mock.js@0.2.0:
    mock.js: private
  mockjs@1.1.0:
    mockjs: private
  ms@2.1.3:
    ms: private
  nano-time@1.0.0:
    nano-time: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  next-tick@1.1.0:
    next-tick: private
  node-fetch-h2@2.3.0:
    node-fetch-h2: private
  node-fetch@2.7.0:
    node-fetch: private
  node-readfiles@0.2.0:
    node-readfiles: private
  node-releases@2.0.19:
    node-releases: private
  npm-run-path@5.3.0:
    npm-run-path: private
  number-to-words@1.2.4:
    number-to-words: private
  nunjucks@3.2.4:
    nunjucks: private
  oas-kit-common@1.0.8:
    oas-kit-common: private
  oas-linter@3.2.2:
    oas-linter: private
  oas-resolver@2.5.6:
    oas-resolver: private
  oas-schema-walker@1.1.5:
    oas-schema-walker: private
  oas-validator@5.0.8:
    oas-validator: private
  object-assign@4.1.1:
    object-assign: private
  oblivious-set@1.0.0:
    oblivious-set: private
  once@1.4.0:
    once: private
  onetime@6.0.0:
    onetime: private
  openapi3-ts@2.0.2:
    openapi3-ts: private
  optionator@0.9.4:
    optionator: private
  p-limit@5.0.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  parent-module@1.0.1:
    parent-module: private
  parse-json@5.2.0:
    parse-json: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-type@4.0.0:
    path-type: private
  pathe@1.1.2:
    pathe: private
  pathval@1.1.1:
    pathval: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pkg-types@1.3.1:
    pkg-types: private
  postcss@8.5.6:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier@2.8.8:
    prettier: private
  pretty-format@29.7.0:
    pretty-format: private
  prop-types@15.8.1:
    prop-types: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  punycode@2.3.1:
    punycode: private
  queue-microtask@1.2.3:
    queue-microtask: private
  rc-cascader@3.34.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-cascader: private
  rc-checkbox@3.5.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-checkbox: private
  rc-collapse@3.9.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-collapse: private
  rc-dialog@9.6.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-dialog: private
  rc-drawer@7.3.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-drawer: private
  rc-dropdown@4.2.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-dropdown: private
  rc-field-form@2.7.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-field-form: private
  rc-image@7.12.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-image: private
  rc-input-number@9.5.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-input-number: private
  rc-input@1.8.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-input: private
  rc-mentions@2.20.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-mentions: private
  rc-menu@9.16.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-menu: private
  rc-motion@2.9.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-motion: private
  rc-notification@5.6.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-notification: private
  rc-overflow@1.4.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-overflow: private
  rc-pagination@5.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-pagination: private
  rc-picker@4.11.3(dayjs@1.11.13)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-picker: private
  rc-progress@4.0.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-progress: private
  rc-rate@2.13.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-rate: private
  rc-resize-observer@1.4.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-resize-observer: private
  rc-segmented@2.7.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-segmented: private
  rc-select@14.16.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-select: private
  rc-slider@11.1.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-slider: private
  rc-steps@6.0.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-steps: private
  rc-switch@4.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-switch: private
  rc-table@7.51.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-table: private
  rc-tabs@15.6.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-tabs: private
  rc-textarea@1.10.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-textarea: private
  rc-tooltip@6.4.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-tooltip: private
  rc-tree-select@5.27.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-tree-select: private
  rc-tree@5.13.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-tree: private
  rc-upload@4.9.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-upload: private
  rc-util@5.44.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-util: private
  rc-virtual-list@3.19.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-virtual-list: private
  react-is@18.3.1:
    react-is: private
  react-refresh@0.17.0:
    react-refresh: private
  react-router@6.30.1(react@18.3.1):
    react-router: private
  react-smooth@4.0.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-smooth: private
  react-transition-group@4.4.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-transition-group: private
  recharts-scale@0.4.5:
    recharts-scale: private
  reftools@1.1.9:
    reftools: private
  remove-accents@0.5.0:
    remove-accents: private
  require-directory@2.1.1:
    require-directory: private
  reserved-words@0.1.2:
    reserved-words: private
  resize-observer-polyfill@1.5.1:
    resize-observer-polyfill: private
  resolve-from@4.0.0:
    resolve-from: private
  reusify@1.1.0:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  rollup@4.45.0:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  scheduler@0.23.2:
    scheduler: private
  scroll-into-view-if-needed@3.1.0:
    scroll-into-view-if-needed: private
  semver@7.7.2:
    semver: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  should-equal@2.0.0:
    should-equal: private
  should-format@3.0.3:
    should-format: private
  should-type-adaptors@1.1.0:
    should-type-adaptors: private
  should-type@1.4.0:
    should-type: private
  should-util@1.0.1:
    should-util: private
  should@13.2.3:
    should: private
  siginfo@2.0.0:
    siginfo: private
  signal-exit@4.1.0:
    signal-exit: private
  slash@3.0.0:
    slash: private
  source-map-js@1.2.1:
    source-map-js: private
  stackback@0.0.2:
    stackback: private
  state-local@1.0.7:
    state-local: private
  std-env@3.9.0:
    std-env: private
  string-convert@0.2.1:
    string-convert: private
  string-width@4.2.3:
    string-width: private
  strip-ansi@6.0.1:
    strip-ansi: private
  strip-final-newline@3.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strip-literal@2.1.1:
    strip-literal: private
  stylis@4.3.6:
    stylis: private
  supports-color@7.2.0:
    supports-color: private
  swagger2openapi@7.0.8:
    swagger2openapi: private
  text-table@0.2.0:
    text-table: private
  throttle-debounce@5.0.2:
    throttle-debounce: private
  timers-ext@0.1.8:
    timers-ext: private
  tiny-invariant@1.3.3:
    tiny-invariant: private
  tiny-pinyin@1.3.2:
    tiny-pinyin: private
  tinybench@2.9.0:
    tinybench: private
  tinypool@0.8.4:
    tinypool: private
  tinyspy@2.2.1:
    tinyspy: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toggle-selection@1.0.6:
    toggle-selection: private
  tr46@0.0.3:
    tr46: private
  ts-api-utils@1.4.3(typescript@5.8.3):
    ts-api-utils: private
  type-check@0.4.0:
    type-check: private
  type-detect@4.1.0:
    type-detect: private
  type-fest@0.20.2:
    type-fest: private
  type@2.7.3:
    type: private
  ufo@1.6.1:
    ufo: private
  unload@2.2.0:
    unload: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  use-sync-external-store@1.5.0(react@18.3.1):
    use-sync-external-store: private
  victory-vendor@36.9.2:
    victory-vendor: private
  vite-node@1.6.1:
    vite-node: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which@2.0.2:
    which: private
  why-is-node-running@2.3.0:
    why-is-node-running: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  y18n@5.0.8:
    y18n: private
  yallist@3.1.1:
    yallist: private
  yaml@1.10.2:
    yaml: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yocto-queue@1.2.1:
    yocto-queue: private
ignoredBuilds:
  - es5-ext
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.6.1
pendingBuilds: []
prunedAt: Tue, 15 Jul 2025 06:19:45 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-ia32@0.21.5'
  - '@esbuild/win32-x64@0.21.5'
  - '@rollup/rollup-android-arm-eabi@4.45.0'
  - '@rollup/rollup-android-arm64@4.45.0'
  - '@rollup/rollup-darwin-x64@4.45.0'
  - '@rollup/rollup-freebsd-arm64@4.45.0'
  - '@rollup/rollup-freebsd-x64@4.45.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.45.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.45.0'
  - '@rollup/rollup-linux-arm64-gnu@4.45.0'
  - '@rollup/rollup-linux-arm64-musl@4.45.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.45.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.45.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.45.0'
  - '@rollup/rollup-linux-riscv64-musl@4.45.0'
  - '@rollup/rollup-linux-s390x-gnu@4.45.0'
  - '@rollup/rollup-linux-x64-gnu@4.45.0'
  - '@rollup/rollup-linux-x64-musl@4.45.0'
  - '@rollup/rollup-win32-arm64-msvc@4.45.0'
  - '@rollup/rollup-win32-ia32-msvc@4.45.0'
  - '@rollup/rollup-win32-x64-msvc@4.45.0'
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
